// ==UserScript==
// @name         Tastien自动数据导出工具
// @namespace    http://tampermonkey.net/
// @version      2.0.0
// @description  自动化导出Tastien平台业务数据，支持批量月份处理，手动触发模式
// <AUTHOR>
// @match        https://tastien.tastientech.com/portal/expand-store/developStores/bunk*
// @grant        none
// @run-at       document-end
// ==/UserScript==

/*
 * 版本: 2.0.0
 * 变更记录:
 * - 2025-01-27: 初始版本，实现基础自动导出功能
 * - 2025-08-01: v2.0.0 重大更新
 *   * 改为手动触发模式，添加浮动控制面板
 *   * 增强错误处理和异常捕获机制
 *   * 优化用户界面和交互体验
 *   * 改进代码结构和可维护性
 *   * 添加详细的日志记录和状态管理
 *   * 增加输入验证和边界条件检查
 */

(function() {
    'use strict';

    // 配置参数
    const CONFIG = {
        WAIT_TIME: 5000,           // 默认等待时间(毫秒)
        SEARCH_WAIT: 3000,         // 搜索后等待时间(优化性能)
        EXPORT_WAIT: 3000,         // 导出后等待时间(优化性能)
        PAGE_LOAD_WAIT: 4000,      // 页面加载等待时间(优化性能)
        CURRENT_YEAR: new Date().getFullYear(), // 自动获取当前年份
        MAX_RETRY_COUNT: 3,        // 最大重试次数
        ELEMENT_TIMEOUT: 15000,    // 元素查找超时时间
        LOG_LEVEL: 'INFO'          // 日志级别: DEBUG, INFO, WARN, ERROR
    };

    // 全局状态管理
    let isRunning = false;
    let isPaused = false;
    let currentMonth = 0;
    let endMonth = 0;
    let progressDialog = null;
    let controlPanel = null;
    let logMessages = [];
    let retryCount = 0;

    // 日志管理系统
    const Logger = {
        log(level, message, data = null) {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = { timestamp, level, message, data };
            logMessages.push(logEntry);

            // 保持日志数量在合理范围内
            if (logMessages.length > 100) {
                logMessages = logMessages.slice(-50);
            }

            // 根据配置的日志级别输出
            const levels = { DEBUG: 0, INFO: 1, WARN: 2, ERROR: 3 };
            if (levels[level] >= levels[CONFIG.LOG_LEVEL]) {
                const prefix = `[${timestamp}] [${level}]`;
                if (data) {
                    console[level.toLowerCase()](prefix, message, data);
                } else {
                    console[level.toLowerCase()](prefix, message);
                }
            }
        },

        debug(message, data) { this.log('DEBUG', message, data); },
        info(message, data) { this.log('INFO', message, data); },
        warn(message, data) { this.log('WARN', message, data); },
        error(message, data) { this.log('ERROR', message, data); }
    };

    // 工具函数：获取月份天数
    function getDaysInMonth(year, month) {
        try {
            const days = new Date(year, month, 0).getDate();
            Logger.debug(`获取${year}年${month}月天数: ${days}`);
            return days;
        } catch (error) {
            Logger.error('获取月份天数失败', error);
            return 31; // 默认返回31天
        }
    }

    // 工具函数：格式化月份为两位数
    function formatMonth(month) {
        if (typeof month !== 'number' || month < 1 || month > 12) {
            Logger.warn(`无效的月份值: ${month}`);
            return '01';
        }
        return month.toString().padStart(2, '0');
    }

    // 工具函数：等待指定时间
    function sleep(ms) {
        if (typeof ms !== 'number' || ms < 0) {
            Logger.warn(`无效的等待时间: ${ms}`);
            ms = 1000;
        }
        Logger.debug(`等待 ${ms}ms`);
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // 工具函数：等待元素出现（增强版）
    function waitForElement(selector, timeout = CONFIG.ELEMENT_TIMEOUT) {
        return new Promise((resolve, reject) => {
            if (!selector || typeof selector !== 'string') {
                reject(new Error('无效的选择器'));
                return;
            }

            const startTime = Date.now();
            Logger.debug(`开始等待元素: ${selector}`);

            function check() {
                try {
                    const element = document.querySelector(selector);
                    if (element && element.offsetParent !== null) { // 确保元素可见
                        Logger.debug(`元素找到: ${selector}`);
                        resolve(element);
                        return;
                    }

                    if (Date.now() - startTime > timeout) {
                        const error = new Error(`元素未找到或不可见: ${selector} (超时: ${timeout}ms)`);
                        Logger.error('等待元素超时', error);
                        reject(error);
                        return;
                    }

                    setTimeout(check, 200); // 增加检查间隔以减少CPU使用
                } catch (error) {
                    Logger.error('等待元素时发生错误', error);
                    reject(error);
                }
            }

            check();
        });
    }

    // 工具函数：安全点击元素
    function safeClick(element, description = '元素') {
        return new Promise((resolve, reject) => {
            try {
                if (!element) {
                    throw new Error(`${description}不存在`);
                }

                if (element.offsetParent === null) {
                    throw new Error(`${description}不可见`);
                }

                // 滚动到元素位置
                element.scrollIntoView({ behavior: 'smooth', block: 'center' });

                setTimeout(() => {
                    try {
                        element.click();
                        Logger.info(`成功点击${description}`);
                        resolve();
                    } catch (clickError) {
                        Logger.error(`点击${description}失败`, clickError);
                        reject(clickError);
                    }
                }, 500);

            } catch (error) {
                Logger.error(`安全点击失败: ${description}`, error);
                reject(error);
            }
        });
    }

    // 创建浮动控制面板
    function createControlPanel() {
        const panel = document.createElement('div');
        panel.id = 'tastien-control-panel';
        panel.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 12px;
            padding: 15px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
            z-index: 10000;
            min-width: 280px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            color: white;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        `;

        panel.innerHTML = `
            <div style="display: flex; align-items: center; margin-bottom: 15px;">
                <h3 style="margin: 0; font-size: 16px; font-weight: 600;">🚀 Tastien导出工具</h3>
                <button id="minimize-btn" style="margin-left: auto; background: rgba(255,255,255,0.2); border: none; border-radius: 50%; width: 24px; height: 24px; color: white; cursor: pointer; font-size: 12px;">−</button>
            </div>
            <div id="panel-content">
                <div style="margin-bottom: 12px;">
                    <button id="start-export-btn" style="width: 100%; padding: 10px; background: #52c41a; color: white; border: none; border-radius: 6px; cursor: pointer; font-weight: 500; transition: all 0.2s;">
                        开始批量导出
                    </button>
                </div>
                <div style="margin-bottom: 12px;">
                    <button id="single-export-btn" style="width: 100%; padding: 10px; background: #1890ff; color: white; border: none; border-radius: 6px; cursor: pointer; font-weight: 500; transition: all 0.2s;">
                        当前页面导出
                    </button>
                </div>
                <div style="font-size: 12px; opacity: 0.8; text-align: center;">
                    状态: <span id="status-text">就绪</span>
                </div>
            </div>
        `;

        document.body.appendChild(panel);

        // 绑定事件
        const minimizeBtn = panel.querySelector('#minimize-btn');
        const panelContent = panel.querySelector('#panel-content');
        let isMinimized = false;

        minimizeBtn.onclick = () => {
            isMinimized = !isMinimized;
            panelContent.style.display = isMinimized ? 'none' : 'block';
            minimizeBtn.textContent = isMinimized ? '+' : '−';
            panel.style.minWidth = isMinimized ? '120px' : '280px';
        };

        panel.querySelector('#start-export-btn').onclick = () => {
            if (!isRunning) {
                showInputDialog();
            } else {
                Logger.warn('导出任务正在进行中');
                alert('导出任务正在进行中，请等待完成或停止当前任务');
            }
        };

        panel.querySelector('#single-export-btn').onclick = () => {
            if (!isRunning) {
                performSingleExport();
            } else {
                Logger.warn('导出任务正在进行中');
                alert('导出任务正在进行中，请等待完成或停止当前任务');
            }
        };

        // 添加悬停效果
        panel.onmouseenter = () => {
            panel.style.transform = 'scale(1.02)';
            panel.style.boxShadow = '0 12px 40px rgba(0,0,0,0.4)';
        };

        panel.onmouseleave = () => {
            panel.style.transform = 'scale(1)';
            panel.style.boxShadow = '0 8px 32px rgba(0,0,0,0.3)';
        };

        Logger.info('控制面板创建完成');
        return panel;
    }

    // 更新控制面板状态
    function updateControlPanelStatus(status) {
        if (controlPanel) {
            const statusText = controlPanel.querySelector('#status-text');
            if (statusText) {
                statusText.textContent = status;
                Logger.debug(`控制面板状态更新: ${status}`);
            }
        }
    }

    // 创建进度对话框（增强版）
    function createProgressDialog() {
        const dialog = document.createElement('div');
        dialog.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border: 2px solid #1890ff;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
            z-index: 10001;
            min-width: 400px;
            max-width: 500px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        `;

        dialog.innerHTML = `
            <div style="display: flex; align-items: center; margin-bottom: 20px;">
                <h3 style="margin: 0; color: #1890ff; font-size: 18px;">📊 数据导出进度</h3>
                <button id="close-progress" style="margin-left: auto; background: #ff4d4f; border: none; border-radius: 50%; width: 28px; height: 28px; color: white; cursor: pointer;">×</button>
            </div>
            <div id="progress-info" style="margin-bottom: 15px; font-size: 14px; color: #666;">准备开始...</div>
            <div style="background: #f0f0f0; height: 24px; border-radius: 12px; overflow: hidden; margin-bottom: 15px; position: relative;">
                <div id="progress-bar" style="height: 100%; background: linear-gradient(90deg, #1890ff, #52c41a); width: 0%; transition: width 0.5s ease;"></div>
                <div id="progress-text" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); font-size: 12px; font-weight: bold; color: #333;">0%</div>
            </div>
            <div style="display: flex; gap: 10px; justify-content: center; margin-bottom: 15px;">
                <button id="pause-btn" style="padding: 8px 16px; background: #faad14; color: white; border: none; border-radius: 6px; cursor: pointer; font-weight: 500;">暂停</button>
                <button id="stop-btn" style="padding: 8px 16px; background: #ff4d4f; color: white; border: none; border-radius: 6px; cursor: pointer; font-weight: 500;">停止</button>
            </div>
            <div id="log-container" style="max-height: 150px; overflow-y: auto; background: #f8f9fa; border-radius: 6px; padding: 10px; font-size: 12px; font-family: monospace;">
                <div style="color: #666;">日志记录将在这里显示...</div>
            </div>
        `;

        document.body.appendChild(dialog);

        // 绑定按钮事件
        dialog.querySelector('#pause-btn').onclick = togglePause;
        dialog.querySelector('#stop-btn').onclick = stopExport;
        dialog.querySelector('#close-progress').onclick = () => {
            if (!isRunning || confirm('确定要关闭进度窗口吗？任务将继续在后台运行。')) {
                dialog.style.display = 'none';
            }
        };

        Logger.info('进度对话框创建完成');
        return dialog;
    }

    // 更新进度（增强版）
    function updateProgress(current, total, message) {
        if (!progressDialog) return;

        try {
            const progressInfo = progressDialog.querySelector('#progress-info');
            const progressBar = progressDialog.querySelector('#progress-bar');
            const progressText = progressDialog.querySelector('#progress-text');
            const logContainer = progressDialog.querySelector('#log-container');

            const percentage = Math.round((current / total) * 100);

            if (progressInfo) {
                progressInfo.textContent = `${message} (${current}/${total})`;
            }

            if (progressBar) {
                progressBar.style.width = `${percentage}%`;
            }

            if (progressText) {
                progressText.textContent = `${percentage}%`;
            }

            // 添加日志到进度窗口
            if (logContainer && message) {
                const logEntry = document.createElement('div');
                logEntry.style.cssText = 'margin-bottom: 2px; color: #333;';
                logEntry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
                logContainer.appendChild(logEntry);
                logContainer.scrollTop = logContainer.scrollHeight;

                // 限制日志条目数量
                const logEntries = logContainer.children;
                if (logEntries.length > 20) {
                    logContainer.removeChild(logEntries[1]); // 保留第一个提示信息
                }
            }

            // 更新控制面板状态
            updateControlPanelStatus(`进行中 ${percentage}%`);

            Logger.info(`进度更新: ${current}/${total} (${percentage}%) - ${message}`);

        } catch (error) {
            Logger.error('更新进度时发生错误', error);
        }
    }

    // 暂停/继续功能（增强版）
    function togglePause() {
        try {
            isPaused = !isPaused;
            const pauseBtn = progressDialog?.querySelector('#pause-btn');

            if (pauseBtn) {
                pauseBtn.textContent = isPaused ? '继续' : '暂停';
                pauseBtn.style.background = isPaused ? '#52c41a' : '#faad14';
            }

            const status = isPaused ? '已暂停' : '进行中';
            updateControlPanelStatus(status);

            Logger.info(`导出任务${isPaused ? '已暂停' : '已继续'}`);

        } catch (error) {
            Logger.error('切换暂停状态时发生错误', error);
        }
    }

    // 停止导出（增强版）
    function stopExport() {
        try {
            const wasRunning = isRunning;
            isRunning = false;
            isPaused = false;
            retryCount = 0;

            if (progressDialog) {
                progressDialog.remove();
                progressDialog = null;
            }

            updateControlPanelStatus('已停止');

            if (wasRunning) {
                Logger.info('导出任务已停止');
                // 显示停止确认
                setTimeout(() => {
                    alert('导出任务已停止');
                }, 100);
            }

        } catch (error) {
            Logger.error('停止导出时发生错误', error);
        }
    }

    // 构建URL（增强版）
    function buildURL(month, year = CONFIG.CURRENT_YEAR) {
        try {
            if (!month || month < 1 || month > 12) {
                throw new Error(`无效的月份: ${month}`);
            }

            const monthStr = formatMonth(month);
            const lastDay = getDaysInMonth(year, month);
            const lastDayStr = lastDay.toString().padStart(2, '0');

            const startDate = `${year}-${monthStr}-01`;
            const endDate = `${year}-${monthStr}-${lastDayStr}`;

            const url = `https://tastien.tastientech.com/portal/expand-store/developStores/bunk?current=1&pageSize=10&submitTime%5B0%5D=${startDate}&submitTime%5B1%5D=${endDate}`;

            Logger.debug(`构建URL: ${month}月 -> ${url}`);
            return url;

        } catch (error) {
            Logger.error('构建URL失败', error);
            throw error;
        }
    }

    // 单页面导出功能
    async function performSingleExport() {
        try {
            updateControlPanelStatus('单页导出中');
            Logger.info('开始单页面导出');

            // 查找并点击搜索按钮
            const searchButton = await waitForElement('button[type="submit"].ant-btn-primary span', 5000);
            if (searchButton && searchButton.textContent.includes('搜 索')) {
                await safeClick(searchButton.parentElement, '搜索按钮');
                await sleep(CONFIG.SEARCH_WAIT);
            } else {
                throw new Error('搜索按钮未找到');
            }

            // 查找并点击导出按钮
            const exportButton = await waitForElement('button[type="button"].ant-btn-primary span', 5000);
            if (exportButton && exportButton.textContent.includes('导出业务数据')) {
                await safeClick(exportButton.parentElement, '导出按钮');
                Logger.info('单页面导出完成');
                updateControlPanelStatus('单页导出完成');

                setTimeout(() => {
                    updateControlPanelStatus('就绪');
                }, 3000);

            } else {
                throw new Error('导出按钮未找到');
            }

        } catch (error) {
            Logger.error('单页面导出失败', error);
            updateControlPanelStatus('导出失败');
            alert(`单页面导出失败: ${error.message}`);

            setTimeout(() => {
                updateControlPanelStatus('就绪');
            }, 3000);
        }
    }

    // 处理单个月份
    async function processMonth(month) {
        try {
            const url = buildURL(month);
            updateProgress(month - currentMonth + 1, endMonth - currentMonth + 1, `正在处理 ${month} 月...`);
            
            // 跳转到对应月份页面
            if (window.location.href !== url) {
                window.location.href = url;
                await sleep(CONFIG.PAGE_LOAD_WAIT);
            }

            // 等待页面加载完成
            await sleep(CONFIG.PAGE_LOAD_WAIT);

            // 查找并点击搜索按钮
            updateProgress(month - currentMonth + 1, endMonth - currentMonth + 1, `${month} 月 - 点击搜索按钮...`);
            
            const searchButton = await waitForElement('button[type="submit"].ant-btn-primary span');
            if (searchButton.textContent.includes('搜 索')) {
                searchButton.parentElement.click();
                console.log(`${month}月: 已点击搜索按钮`);
            } else {
                throw new Error('搜索按钮未找到');
            }

            await sleep(CONFIG.SEARCH_WAIT);

            // 检查暂停状态
            while (isPaused && isRunning) {
                await sleep(1000);
            }

            if (!isRunning) return;

            // 查找并点击导出按钮
            updateProgress(month - currentMonth + 1, endMonth - currentMonth + 1, `${month} 月 - 点击导出按钮...`);
            
            const exportButton = await waitForElement('button[type="button"].ant-btn-primary span');
            if (exportButton.textContent.includes('导出业务数据')) {
                exportButton.parentElement.click();
                console.log(`${month}月: 已点击导出按钮`);
            } else {
                throw new Error('导出按钮未找到');
            }

            await sleep(CONFIG.EXPORT_WAIT);
            
            updateProgress(month - currentMonth + 1, endMonth - currentMonth + 1, `${month} 月 - 完成导出`);
            console.log(`${month}月: 导出完成`);

        } catch (error) {
            console.error(`处理${month}月时出错:`, error);
            updateProgress(month - currentMonth + 1, endMonth - currentMonth + 1, `${month} 月 - 出错: ${error.message}`);
            
            // 询问是否继续
            const shouldContinue = confirm(`处理${month}月时出错: ${error.message}\n\n是否继续处理下一个月份？`);
            if (!shouldContinue) {
                stopExport();
                return;
            }
        }
    }

    // 主导出流程
    async function startExport(startMonth, endMonth) {
        isRunning = true;
        currentMonth = startMonth;
        
        progressDialog = createProgressDialog();
        
        try {
            for (let month = startMonth; month <= endMonth && isRunning; month++) {
                // 检查暂停状态
                while (isPaused && isRunning) {
                    await sleep(1000);
                }
                
                if (!isRunning) break;
                
                await processMonth(month);
            }
            
            if (isRunning) {
                updateProgress(endMonth - startMonth + 1, endMonth - startMonth + 1, '所有月份导出完成！');
                setTimeout(() => {
                    alert('所有月份数据导出完成！');
                    stopExport();
                }, 2000);
            }
            
        } catch (error) {
            console.error('导出过程中出现错误:', error);
            alert(`导出过程中出现错误: ${error.message}`);
        } finally {
            isRunning = false;
        }
    }

    // 输入验证
    function validateInput(startMonth, endMonth) {
        const start = parseInt(startMonth);
        const end = parseInt(endMonth);
        
        if (isNaN(start) || isNaN(end)) {
            return { valid: false, message: '请输入有效的数字' };
        }
        
        if (start < 1 || start > 12 || end < 1 || end > 12) {
            return { valid: false, message: '月份必须在1-12之间' };
        }
        
        if (start > end) {
            return { valid: false, message: '开始月份不能大于结束月份' };
        }
        
        return { valid: true, start, end };
    }

    // 显示输入对话框
    function showInputDialog() {
        const startMonth = prompt('请输入开始月份 (1-12):', '1');
        if (startMonth === null) return;
        
        const endMonth = prompt('请输入结束月份 (1-12):', '12');
        if (endMonth === null) return;
        
        const validation = validateInput(startMonth, endMonth);
        if (!validation.valid) {
            alert(validation.message);
            return showInputDialog();
        }
        
        const confirmMsg = `确认导出 ${validation.start} 月到 ${validation.end} 月的数据吗？\n\n注意：此过程将自动进行，请不要手动操作页面。`;
        if (confirm(confirmMsg)) {
            startExport(validation.start, validation.end);
        }
    }

    // 检查URL匹配并启动脚本
    function checkAndStart() {
        const targetURL = 'https://tastien.tastientech.com/portal/expand-store/developStores/bunk?current=1&pageSize=10';
        
        if (window.location.href.startsWith(targetURL)) {
            console.log('Tastien自动导出脚本已启动');
            
            // 延迟启动，确保页面完全加载
            setTimeout(() => {
                if (!isRunning) {
                    showInputDialog();
                }
            }, 2000);
        }
    }

    // 初始化脚本
    checkAndStart();

})();